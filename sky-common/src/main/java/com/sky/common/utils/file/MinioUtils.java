package com.sky.common.utils.file;

import com.sky.common.config.MinioConfig;
import com.sky.common.exception.ServiceException;
import com.sky.common.utils.StringUtils;
import com.sky.common.utils.uuid.UUID;
import io.minio.*;
import io.minio.http.Method;
import io.minio.messages.Bucket;
import io.minio.messages.Item;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Minio 文件存储工具类
 *
 * <AUTHOR>
 */
@Component
public class MinioUtils {
    @Autowired
    private MinioClient minioClient;

    @Autowired
    private MinioConfig minioConfig;

    /**
     * 查看存储bucket是否存在
     *
     * @return boolean
     */
    public boolean bucketExists(String bucketName) {
        boolean found;
        try {
            found = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
        } catch (Exception e) {
            throw new ServiceException("检查存储bucket是否存在失败", e);
        }
        return found;
    }

    /**
     * 创建存储bucket
     *
     * @param bucketName 存储bucket名称
     * @return Boolean
     */
    public boolean makeBucket(String bucketName) {
        try {
            minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
        } catch (Exception e) {
            throw new ServiceException("创建存储bucket失败", e);
        }
        return true;
    }

    /**
     * 删除存储bucket
     *
     * @param bucketName 存储bucket名称
     * @return Boolean
     */
    public boolean removeBucket(String bucketName) {
        try {
            minioClient.removeBucket(RemoveBucketArgs.builder().bucket(bucketName).build());
        } catch (Exception e) {
            throw new ServiceException("删除存储bucket失败", e);
        }
        return true;
    }

    /**
     * 获取全部bucket
     *
     * @return List<Bucket>
     */
    public List<Bucket> getAllBuckets() {
        try {
            return minioClient.listBuckets();
        } catch (Exception e) {
            throw new ServiceException("获取全部bucket失败", e);
        }
    }

    /**
     * 文件上传
     *
     * @param file 文件
     * @return 文件名
     */
    public String upload(MultipartFile file) {
        String fileName = extractFilename(file);
        try {
            PutObjectArgs args = PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(fileName)
                    .stream(file.getInputStream(), file.getSize(), -1)
                    .contentType(file.getContentType())
                    .build();
            minioClient.putObject(args);
        } catch (Exception e) {
            throw new ServiceException("上传文件失败", e);
        }
        return fileName;
    }

    /**
     * 文件上传
     *
     * @param data 数据
     * @param objectName 对象名
     * @param contentType 内容类型
     * @return 文件名
     */
    public String upload(byte[] data, String objectName, String contentType) {
        ByteArrayInputStream bais = new ByteArrayInputStream(data);
        try {
            PutObjectArgs args = PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .stream(bais, data.length, -1)
                    .contentType(contentType)
                    .build();
            minioClient.putObject(args);
        } catch (Exception e) {
            throw new ServiceException("上传文件失败", e);
        }
        return objectName;
    }

    /**
     * 获取文件
     *
     * @param fileName 文件名称
     * @return InputStream
     */
    public InputStream getObject(String fileName) {
        try {
            return minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .object(fileName)
                            .build());
        } catch (Exception e) {
            throw new ServiceException("获取文件失败", e);
        }
    }

    /**
     * 删除文件
     *
     * @param fileName 文件名称
     * @return boolean
     */
    public boolean removeObject(String fileName) {
        try {
            minioClient.removeObject(
                    RemoveObjectArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .object(fileName)
                            .build());
        } catch (Exception e) {
            throw new ServiceException("删除文件失败", e);
        }
        return true;
    }

    /**
     * 获取文件外链
     *
     * @param fileName 文件名称
     * @param expiry 过期时间，默认7天 单位天
     * @return url
     */
    public String getObjectUrl(String fileName, Integer expiry) {
        try {
            expiry = expiry == null ? 7 : expiry;
            return minioClient.getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                            .method(Method.GET)
                            .bucket(minioConfig.getBucketName())
                            .object(fileName)
                            .expiry(expiry, TimeUnit.DAYS)
                            .build());
        } catch (Exception e) {
            throw new ServiceException("获取文件外链失败", e);
        }
    }

    /**
     * 获取文件列表
     *
     * @param prefix 前缀
     * @param recursive 是否递归查询
     * @return 文件列表
     */
    public List<Item> listObjects(String prefix, boolean recursive) {
        List<Item> list = new ArrayList<>();
        try {
            Iterable<Result<Item>> objectsIterator = minioClient.listObjects(
                    ListObjectsArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .prefix(prefix)
                            .recursive(recursive)
                            .build());
            for (Result<Item> result : objectsIterator) {
                Item item = result.get();
                list.add(item);
            }
        } catch (Exception e) {
            throw new ServiceException("获取文件列表失败", e);
        }
        return list;
    }

    /**
     * 编码文件名
     */
    public String extractFilename(MultipartFile file) {
        String extension = getExtension(file);
        return DateUtils.datePath() + "/" + UUID.randomUUID() + "." + extension;
    }

    /**
     * 获取文件后缀
     */
    public String getExtension(MultipartFile file) {
        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
        if (StringUtils.isEmpty(extension)) {
            extension = MimeTypeUtils.getExtension(file.getContentType());
        }
        return extension;
    }
}