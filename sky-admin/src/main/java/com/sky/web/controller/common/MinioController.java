package com.sky.web.controller.common;

import com.sky.common.config.MinioConfig;
import com.sky.common.core.domain.AjaxResult;
import com.sky.common.utils.file.MinioUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * Minio文件上传控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/common/minio")
public class MinioController {
    @Autowired
    private MinioConfig minioConfig;
    
    @Autowired
    private MinioUtils minioUtils;

    /**
     * 文件上传
     */
    @PostMapping("/upload")
    public AjaxResult upload(MultipartFile file) {
        try {
            // 上传文件
            String fileName = minioUtils.upload(file);
            String url = minioUtils.getObjectUrl(fileName, null);
            AjaxResult ajax = AjaxResult.success();
            ajax.put("fileName", fileName);
            ajax.put("url", url);
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }
}