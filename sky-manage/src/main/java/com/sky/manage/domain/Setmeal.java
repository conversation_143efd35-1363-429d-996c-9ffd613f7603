package com.sky.manage.domain;

import java.math.BigDecimal;
import java.util.List;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sky.common.annotation.Excel;
import com.sky.common.core.domain.BaseEntity;

/**
 * 套餐g管理对象 setmeal
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public class Setmeal extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 套餐ID */
    private Long setmealId;

    /** 套餐名称 */
    @Excel(name = "套餐名称")
    private String setmealName;

    /** 套餐分类ID */
    @Excel(name = "套餐分类ID")
    private Long categoryId;

    /** 套餐图片 */
    @Excel(name = "套餐图片")
    private String image;

    /** 套餐描述 */
    @Excel(name = "套餐描述")
    private String description;

    /** 套餐价格 */
    @Excel(name = "套餐价格")
    private BigDecimal price;

    /** 售卖状态（0正常 1停售） */
    @Excel(name = "售卖状态", readConverterExp = "0=正常,1=停售")
    private String status;

    /** 套餐菜品关系信息 */
    private List<SetmealDish> setmealDishList;

    public void setSetmealId(Long setmealId) 
    {
        this.setmealId = setmealId;
    }

    public Long getSetmealId() 
    {
        return setmealId;
    }

    public void setSetmealName(String setmealName) 
    {
        this.setmealName = setmealName;
    }

    public String getSetmealName() 
    {
        return setmealName;
    }

    public void setCategoryId(Long categoryId) 
    {
        this.categoryId = categoryId;
    }

    public Long getCategoryId() 
    {
        return categoryId;
    }

    public void setImage(String image) 
    {
        this.image = image;
    }

    public String getImage() 
    {
        return image;
    }

    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }

    public void setPrice(BigDecimal price) 
    {
        this.price = price;
    }

    public BigDecimal getPrice() 
    {
        return price;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public List<SetmealDish> getSetmealDishList()
    {
        return setmealDishList;
    }

    public void setSetmealDishList(List<SetmealDish> setmealDishList)
    {
        this.setmealDishList = setmealDishList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("setmealId", getSetmealId())
            .append("setmealName", getSetmealName())
            .append("categoryId", getCategoryId())
            .append("image", getImage())
            .append("description", getDescription())
            .append("price", getPrice())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("setmealDishList", getSetmealDishList())
            .toString();
    }
}
