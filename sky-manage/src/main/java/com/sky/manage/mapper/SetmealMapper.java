package com.sky.manage.mapper;

import java.util.List;
import com.sky.manage.domain.Setmeal;
import com.sky.manage.domain.SetmealDish;

/**
 * 套餐g管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface SetmealMapper 
{
    /**
     * 查询套餐g管理
     * 
     * @param setmealId 套餐g管理主键
     * @return 套餐g管理
     */
    public Setmeal selectSetmealBySetmealId(Long setmealId);

    /**
     * 查询套餐g管理列表
     * 
     * @param setmeal 套餐g管理
     * @return 套餐g管理集合
     */
    public List<Setmeal> selectSetmealList(Setmeal setmeal);

    /**
     * 新增套餐g管理
     * 
     * @param setmeal 套餐g管理
     * @return 结果
     */
    public int insertSetmeal(Setmeal setmeal);

    /**
     * 修改套餐g管理
     * 
     * @param setmeal 套餐g管理
     * @return 结果
     */
    public int updateSetmeal(Setmeal setmeal);

    /**
     * 删除套餐g管理
     * 
     * @param setmealId 套餐g管理主键
     * @return 结果
     */
    public int deleteSetmealBySetmealId(Long setmealId);

    /**
     * 批量删除套餐g管理
     * 
     * @param setmealIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSetmealBySetmealIds(Long[] setmealIds);

    /**
     * 批量删除套餐菜品关系
     * 
     * @param setmealIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSetmealDishBySetmealIds(Long[] setmealIds);
    
    /**
     * 批量新增套餐菜品关系
     * 
     * @param setmealDishList 套餐菜品关系列表
     * @return 结果
     */
    public int batchSetmealDish(List<SetmealDish> setmealDishList);
    

    /**
     * 通过套餐g管理主键删除套餐菜品关系信息
     * 
     * @param setmealId 套餐g管理ID
     * @return 结果
     */
    public int deleteSetmealDishBySetmealId(Long setmealId);
}
