package com.sky.manage.mapper;

import java.util.List;
import com.sky.manage.domain.Dish;

/**
 * 菜品管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface DishMapper 
{
    /**
     * 查询菜品管理
     * 
     * @param dishId 菜品管理主键
     * @return 菜品管理
     */
    public Dish selectDishByDishId(Long dishId);

    /**
     * 查询菜品管理列表
     * 
     * @param dish 菜品管理
     * @return 菜品管理集合
     */
    public List<Dish> selectDishList(Dish dish);

    /**
     * 新增菜品管理
     * 
     * @param dish 菜品管理
     * @return 结果
     */
    public int insertDish(Dish dish);

    /**
     * 修改菜品管理
     * 
     * @param dish 菜品管理
     * @return 结果
     */
    public int updateDish(Dish dish);

    /**
     * 删除菜品管理
     * 
     * @param dishId 菜品管理主键
     * @return 结果
     */
    public int deleteDishByDishId(Long dishId);

    /**
     * 批量删除菜品管理
     * 
     * @param dishIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDishByDishIds(Long[] dishIds);
}
