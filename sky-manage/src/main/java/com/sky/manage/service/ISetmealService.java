package com.sky.manage.service;

import java.util.List;
import com.sky.manage.domain.Setmeal;

/**
 * 套餐g管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface ISetmealService 
{
    /**
     * 查询套餐g管理
     * 
     * @param setmealId 套餐g管理主键
     * @return 套餐g管理
     */
    public Setmeal selectSetmealBySetmealId(Long setmealId);

    /**
     * 查询套餐g管理列表
     * 
     * @param setmeal 套餐g管理
     * @return 套餐g管理集合
     */
    public List<Setmeal> selectSetmealList(Setmeal setmeal);

    /**
     * 新增套餐g管理
     * 
     * @param setmeal 套餐g管理
     * @return 结果
     */
    public int insertSetmeal(Setmeal setmeal);

    /**
     * 修改套餐g管理
     * 
     * @param setmeal 套餐g管理
     * @return 结果
     */
    public int updateSetmeal(Setmeal setmeal);

    /**
     * 批量删除套餐g管理
     * 
     * @param setmealIds 需要删除的套餐g管理主键集合
     * @return 结果
     */
    public int deleteSetmealBySetmealIds(Long[] setmealIds);

    /**
     * 删除套餐g管理信息
     * 
     * @param setmealId 套餐g管理主键
     * @return 结果
     */
    public int deleteSetmealBySetmealId(Long setmealId);
}
