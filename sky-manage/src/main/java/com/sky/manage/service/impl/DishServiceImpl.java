package com.sky.manage.service.impl;

import java.util.List;
import com.sky.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sky.manage.mapper.DishMapper;
import com.sky.manage.domain.Dish;
import com.sky.manage.service.IDishService;

/**
 * 菜品管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class DishServiceImpl implements IDishService 
{
    @Autowired
    private DishMapper dishMapper;

    /**
     * 查询菜品管理
     * 
     * @param dishId 菜品管理主键
     * @return 菜品管理
     */
    @Override
    public Dish selectDishByDishId(Long dishId)
    {
        return dishMapper.selectDishByDishId(dishId);
    }

    /**
     * 查询菜品管理列表
     * 
     * @param dish 菜品管理
     * @return 菜品管理
     */
    @Override
    public List<Dish> selectDishList(Dish dish)
    {
        return dishMapper.selectDishList(dish);
    }

    /**
     * 新增菜品管理
     * 
     * @param dish 菜品管理
     * @return 结果
     */
    @Override
    public int insertDish(Dish dish)
    {
        dish.setCreateTime(DateUtils.getNowDate());
        return dishMapper.insertDish(dish);
    }

    /**
     * 修改菜品管理
     * 
     * @param dish 菜品管理
     * @return 结果
     */
    @Override
    public int updateDish(Dish dish)
    {
        dish.setUpdateTime(DateUtils.getNowDate());
        return dishMapper.updateDish(dish);
    }

    /**
     * 批量删除菜品管理
     * 
     * @param dishIds 需要删除的菜品管理主键
     * @return 结果
     */
    @Override
    public int deleteDishByDishIds(Long[] dishIds)
    {
        return dishMapper.deleteDishByDishIds(dishIds);
    }

    /**
     * 删除菜品管理信息
     * 
     * @param dishId 菜品管理主键
     * @return 结果
     */
    @Override
    public int deleteDishByDishId(Long dishId)
    {
        return dishMapper.deleteDishByDishId(dishId);
    }
}
