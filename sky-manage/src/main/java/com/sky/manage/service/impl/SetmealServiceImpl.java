package com.sky.manage.service.impl;

import java.util.List;
import com.sky.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import com.sky.common.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import com.sky.manage.domain.SetmealDish;
import com.sky.manage.mapper.SetmealMapper;
import com.sky.manage.domain.Setmeal;
import com.sky.manage.service.ISetmealService;

/**
 * 套餐g管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class SetmealServiceImpl implements ISetmealService 
{
    @Autowired
    private SetmealMapper setmealMapper;

    /**
     * 查询套餐g管理
     * 
     * @param setmealId 套餐g管理主键
     * @return 套餐g管理
     */
    @Override
    public Setmeal selectSetmealBySetmealId(Long setmealId)
    {
        return setmealMapper.selectSetmealBySetmealId(setmealId);
    }

    /**
     * 查询套餐g管理列表
     * 
     * @param setmeal 套餐g管理
     * @return 套餐g管理
     */
    @Override
    public List<Setmeal> selectSetmealList(Setmeal setmeal)
    {
        return setmealMapper.selectSetmealList(setmeal);
    }

    /**
     * 新增套餐g管理
     * 
     * @param setmeal 套餐g管理
     * @return 结果
     */
    @Transactional
    @Override
    public int insertSetmeal(Setmeal setmeal)
    {
        setmeal.setCreateTime(DateUtils.getNowDate());
        int rows = setmealMapper.insertSetmeal(setmeal);
        insertSetmealDish(setmeal);
        return rows;
    }

    /**
     * 修改套餐g管理
     * 
     * @param setmeal 套餐g管理
     * @return 结果
     */
    @Transactional
    @Override
    public int updateSetmeal(Setmeal setmeal)
    {
        setmeal.setUpdateTime(DateUtils.getNowDate());
        setmealMapper.deleteSetmealDishBySetmealId(setmeal.getSetmealId());
        insertSetmealDish(setmeal);
        return setmealMapper.updateSetmeal(setmeal);
    }

    /**
     * 批量删除套餐g管理
     * 
     * @param setmealIds 需要删除的套餐g管理主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteSetmealBySetmealIds(Long[] setmealIds)
    {
        setmealMapper.deleteSetmealDishBySetmealIds(setmealIds);
        return setmealMapper.deleteSetmealBySetmealIds(setmealIds);
    }

    /**
     * 删除套餐g管理信息
     * 
     * @param setmealId 套餐g管理主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteSetmealBySetmealId(Long setmealId)
    {
        setmealMapper.deleteSetmealDishBySetmealId(setmealId);
        return setmealMapper.deleteSetmealBySetmealId(setmealId);
    }

    /**
     * 新增套餐菜品关系信息
     * 
     * @param setmeal 套餐g管理对象
     */
    public void insertSetmealDish(Setmeal setmeal)
    {
        List<SetmealDish> setmealDishList = setmeal.getSetmealDishList();
        Long setmealId = setmeal.getSetmealId();
        if (StringUtils.isNotNull(setmealDishList))
        {
            List<SetmealDish> list = new ArrayList<SetmealDish>();
            for (SetmealDish setmealDish : setmealDishList)
            {
                setmealDish.setSetmealId(setmealId);
                list.add(setmealDish);
            }
            if (list.size() > 0)
            {
                setmealMapper.batchSetmealDish(list);
            }
        }
    }
}
