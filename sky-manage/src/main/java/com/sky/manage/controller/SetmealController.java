package com.sky.manage.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sky.common.annotation.Log;
import com.sky.common.core.controller.BaseController;
import com.sky.common.core.domain.AjaxResult;
import com.sky.common.enums.BusinessType;
import com.sky.manage.domain.Setmeal;
import com.sky.manage.service.ISetmealService;
import com.sky.common.utils.poi.ExcelUtil;
import com.sky.common.core.page.TableDataInfo;

/**
 * 套餐g管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@RestController
@RequestMapping("/manage/setmeal")
public class SetmealController extends BaseController
{
    @Autowired
    private ISetmealService setmealService;

    /**
     * 查询套餐g管理列表
     */
    @PreAuthorize("@ss.hasPermi('manage:setmeal:list')")
    @GetMapping("/list")
    public TableDataInfo list(Setmeal setmeal)
    {
        startPage();
        List<Setmeal> list = setmealService.selectSetmealList(setmeal);
        return getDataTable(list);
    }

    /**
     * 导出套餐g管理列表
     */
    @PreAuthorize("@ss.hasPermi('manage:setmeal:export')")
    @Log(title = "套餐g管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Setmeal setmeal)
    {
        List<Setmeal> list = setmealService.selectSetmealList(setmeal);
        ExcelUtil<Setmeal> util = new ExcelUtil<Setmeal>(Setmeal.class);
        util.exportExcel(response, list, "套餐g管理数据");
    }

    /**
     * 获取套餐g管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('manage:setmeal:query')")
    @GetMapping(value = "/{setmealId}")
    public AjaxResult getInfo(@PathVariable("setmealId") Long setmealId)
    {
        return success(setmealService.selectSetmealBySetmealId(setmealId));
    }

    /**
     * 新增套餐g管理
     */
    @PreAuthorize("@ss.hasPermi('manage:setmeal:add')")
    @Log(title = "套餐g管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Setmeal setmeal)
    {
        return toAjax(setmealService.insertSetmeal(setmeal));
    }

    /**
     * 修改套餐g管理
     */
    @PreAuthorize("@ss.hasPermi('manage:setmeal:edit')")
    @Log(title = "套餐g管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Setmeal setmeal)
    {
        return toAjax(setmealService.updateSetmeal(setmeal));
    }

    /**
     * 删除套餐g管理
     */
    @PreAuthorize("@ss.hasPermi('manage:setmeal:remove')")
    @Log(title = "套餐g管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{setmealIds}")
    public AjaxResult remove(@PathVariable Long[] setmealIds)
    {
        return toAjax(setmealService.deleteSetmealBySetmealIds(setmealIds));
    }
}
