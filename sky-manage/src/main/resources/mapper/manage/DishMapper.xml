<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sky.manage.mapper.DishMapper">
    
    <resultMap type="Dish" id="DishResult">
        <result property="dishId"    column="dish_id"    />
        <result property="dishName"    column="dish_name"    />
        <result property="categoryId"    column="category_id"    />
        <result property="image"    column="image"    />
        <result property="description"    column="description"    />
        <result property="price"    column="price"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDishVo">
        select dish_id, dish_name, category_id, image, description, price, status, create_by, create_time, update_by, update_time from dish
    </sql>

    <select id="selectDishList" parameterType="Dish" resultMap="DishResult">
        <include refid="selectDishVo"/>
        <where>  
            <if test="dishName != null  and dishName != ''"> and dish_name like concat('%', #{dishName}, '%')</if>
            <if test="image != null  and image != ''"> and image = #{image}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectDishByDishId" parameterType="Long" resultMap="DishResult">
        <include refid="selectDishVo"/>
        where dish_id = #{dishId}
    </select>

    <insert id="insertDish" parameterType="Dish" useGeneratedKeys="true" keyProperty="dishId">
        insert into dish
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dishName != null and dishName != ''">dish_name,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="image != null and image != ''">image,</if>
            <if test="description != null">description,</if>
            <if test="price != null">price,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dishName != null and dishName != ''">#{dishName},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="image != null and image != ''">#{image},</if>
            <if test="description != null">#{description},</if>
            <if test="price != null">#{price},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDish" parameterType="Dish">
        update dish
        <trim prefix="SET" suffixOverrides=",">
            <if test="dishName != null and dishName != ''">dish_name = #{dishName},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="image != null and image != ''">image = #{image},</if>
            <if test="description != null">description = #{description},</if>
            <if test="price != null">price = #{price},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where dish_id = #{dishId}
    </update>

    <delete id="deleteDishByDishId" parameterType="Long">
        delete from dish where dish_id = #{dishId}
    </delete>

    <delete id="deleteDishByDishIds" parameterType="String">
        delete from dish where dish_id in 
        <foreach item="dishId" collection="array" open="(" separator="," close=")">
            #{dishId}
        </foreach>
    </delete>
</mapper>