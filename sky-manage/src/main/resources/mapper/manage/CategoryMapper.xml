<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sky.manage.mapper.CategoryMapper">
    
    <resultMap type="Category" id="CategoryResult">
        <result property="categoryId"    column="category_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="categoryType"    column="category_type"    />
        <result property="orderNum"    column="order_num"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCategoryVo">
        select category_id, category_name, category_type, order_num, create_by, create_time, update_by, update_time from category
    </sql>

    <select id="selectCategoryList" parameterType="Category" resultMap="CategoryResult">
        <include refid="selectCategoryVo"/>
        <where>  
            <if test="categoryName != null  and categoryName != ''"> and category_name like concat('%', #{categoryName}, '%')</if>
            <if test="categoryType != null  and categoryType != ''"> and category_type = #{categoryType}</if>
        </where>
    </select>
    
    <select id="selectCategoryByCategoryId" parameterType="Long" resultMap="CategoryResult">
        <include refid="selectCategoryVo"/>
        where category_id = #{categoryId}
    </select>

    <insert id="insertCategory" parameterType="Category" useGeneratedKeys="true" keyProperty="categoryId">
        insert into category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="categoryType != null and categoryType != ''">category_type,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="categoryType != null and categoryType != ''">#{categoryType},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCategory" parameterType="Category">
        update category
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name = #{categoryName},</if>
            <if test="categoryType != null and categoryType != ''">category_type = #{categoryType},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where category_id = #{categoryId}
    </update>

    <delete id="deleteCategoryByCategoryId" parameterType="Long">
        delete from category where category_id = #{categoryId}
    </delete>

    <delete id="deleteCategoryByCategoryIds" parameterType="String">
        delete from category where category_id in 
        <foreach item="categoryId" collection="array" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </delete>
</mapper>