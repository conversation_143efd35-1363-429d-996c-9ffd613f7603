<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sky.manage.mapper.SetmealMapper">
    
    <resultMap type="Setmeal" id="SetmealResult">
        <result property="setmealId"    column="setmeal_id"    />
        <result property="setmealName"    column="setmeal_name"    />
        <result property="categoryId"    column="category_id"    />
        <result property="image"    column="image"    />
        <result property="description"    column="description"    />
        <result property="price"    column="price"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap id="SetmealSetmealDishResult" type="Setmeal" extends="SetmealResult">
        <collection property="setmealDishList" ofType="SetmealDish" column="setmeal_id" select="selectSetmealDishList" />
    </resultMap>

    <resultMap type="SetmealDish" id="SetmealDishResult">
        <result property="id"    column="id"    />
        <result property="setmealId"    column="setmeal_id"    />
        <result property="dishId"    column="dish_id"    />
        <result property="price"    column="price"    />
        <result property="copies"    column="copies"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectSetmealVo">
        select setmeal_id, setmeal_name, category_id, image, description, price, status, create_by, create_time, update_by, update_time from setmeal
    </sql>

    <select id="selectSetmealList" parameterType="Setmeal" resultMap="SetmealResult">
        <include refid="selectSetmealVo"/>
        <where>  
            <if test="setmealName != null  and setmealName != ''"> and setmeal_name like concat('%', #{setmealName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectSetmealBySetmealId" parameterType="Long" resultMap="SetmealSetmealDishResult">
        select setmeal_id, setmeal_name, category_id, image, description, price, status, create_by, create_time, update_by, update_time
        from setmeal
        where setmeal_id = #{setmealId}
    </select>

    <select id="selectSetmealDishList" resultMap="SetmealDishResult">
        select id, setmeal_id, dish_id, price, copies, create_time, update_time, create_by, update_by
        from setmeal_dish
        where setmeal_id = #{setmeal_id}
    </select>

    <insert id="insertSetmeal" parameterType="Setmeal" useGeneratedKeys="true" keyProperty="setmealId">
        insert into setmeal
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="setmealName != null and setmealName != ''">setmeal_name,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="image != null">image,</if>
            <if test="description != null">description,</if>
            <if test="price != null">price,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="setmealName != null and setmealName != ''">#{setmealName},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="image != null">#{image},</if>
            <if test="description != null">#{description},</if>
            <if test="price != null">#{price},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSetmeal" parameterType="Setmeal">
        update setmeal
        <trim prefix="SET" suffixOverrides=",">
            <if test="setmealName != null and setmealName != ''">setmeal_name = #{setmealName},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="image != null">image = #{image},</if>
            <if test="description != null">description = #{description},</if>
            <if test="price != null">price = #{price},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where setmeal_id = #{setmealId}
    </update>

    <delete id="deleteSetmealBySetmealId" parameterType="Long">
        delete from setmeal where setmeal_id = #{setmealId}
    </delete>

    <delete id="deleteSetmealBySetmealIds" parameterType="String">
        delete from setmeal where setmeal_id in 
        <foreach item="setmealId" collection="array" open="(" separator="," close=")">
            #{setmealId}
        </foreach>
    </delete>
    
    <delete id="deleteSetmealDishBySetmealIds" parameterType="String">
        delete from setmeal_dish where setmeal_id in 
        <foreach item="setmealId" collection="array" open="(" separator="," close=")">
            #{setmealId}
        </foreach>
    </delete>

    <delete id="deleteSetmealDishBySetmealId" parameterType="Long">
        delete from setmeal_dish where setmeal_id = #{setmealId}
    </delete>

    <insert id="batchSetmealDish">
        insert into setmeal_dish( id, setmeal_id, dish_id, price, copies, create_time, update_time, create_by, update_by) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.id}, #{item.setmealId}, #{item.dishId}, #{item.price}, #{item.copies}, #{item.createTime}, #{item.updateTime}, #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>
</mapper>